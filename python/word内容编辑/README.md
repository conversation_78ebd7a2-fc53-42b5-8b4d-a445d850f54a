# Word内容编辑 MCP服务

专门用于Word文档内容编辑操作的MCP服务，提供表格、文本搜索替换、标题插入、段落编辑等高级内容编辑功能。

## 功能特性

### ✏️ 内容编辑工具 (7个)
- `add_table_tool` - 向文档添加表格，支持自定义行列数和数据填充
- `search_and_replace_tool` - 在文档中搜索并替换文本
- `insert_header_near_text` - 在指定文本或段落索引附近插入标题
- `insert_line_or_paragraph_near_text` - 在指定位置插入新行或段落
- `insert_numbered_list_near_text` - 在指定位置插入编号列表
- `get_paragraph_text_from_document_tool` - 获取指定段落的文本内容
- `find_text_in_document_tool` - 在文档中查找文本并返回位置信息

## 技术特性

- 基于 **FastMCP** 框架构建，提供高性能的MCP服务
- 使用 **python-docx** 库进行Word文档操作
- 支持文件权限检查和错误处理
- 自动处理文档扩展名和路径规范化
- 智能跳过目录(TOC)段落，避免意外修改

## 安装和配置

### 环境要求
- Python 3.10+
- 依赖包：python-docx, fastmcp, lxml, Pillow

### 本地安装
```bash
cd python/word内容编辑
pip install -e .
```

### 使用uv安装（推荐）
```bash
cd python/word内容编辑
uv sync
```

### Claude Desktop配置
在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "word-content-editing": {
      "command": "python",
      "args": ["-m", "word_content_editing.main"],
      "cwd": "/path/to/python/word内容编辑"
    }
  }
}
```

### 使用uv运行
```json
{
  "mcpServers": {
    "word-content-editing": {
      "command": "uv",
      "args": ["run", "python", "-m", "word_content_editing.main"],
      "cwd": "/path/to/python/word内容编辑"
    }
  }
}
```

## 使用示例

### 表格操作
- "在文档中创建一个3x4的表格"
- "添加一个包含数据的表格：[['姓名', '年龄'], ['张三', '25'], ['李四', '30']]"

### 文本搜索和替换
- "将文档中的'旧术语'替换为'新术语'"
- "查找文档中所有'重要'文本的位置"

### 内容插入
- "在'重要提示'文本后插入二级标题：'注意事项'"
- "在第3段后插入新段落：'这是补充内容'"
- "在'概述'文本附近插入编号列表：['第一点', '第二点', '第三点']"

### 文档查询
- "获取第5个段落的文本内容"
- "查找文档中包含'项目'的所有位置"

## API参考

### add_table_tool
向Word文档添加表格。

**参数：**
- `filename` (str): Word文档路径
- `rows` (int): 表格行数
- `cols` (int): 表格列数
- `data` (list, 可选): 二维数组数据来填充表格

**返回：** 操作结果消息

### search_and_replace_tool
在文档中搜索并替换所有匹配的文本。

**参数：**
- `filename` (str): Word文档路径
- `find_text` (str): 要搜索的文本
- `replace_text` (str): 要替换的文本

**返回：** 替换操作结果和替换次数

### insert_header_near_text
在指定文本或段落索引附近插入标题。

**参数：**
- `filename` (str): Word文档路径
- `target_text` (str, 可选): 目标文本
- `header_title` (str, 可选): 标题文本
- `position` (str): 插入位置，'before' 或 'after'（默认）
- `header_style` (str): 标题样式（默认 'Heading 1'）
- `target_paragraph_index` (int, 可选): 目标段落索引

**返回：** 插入操作结果消息

### insert_line_or_paragraph_near_text
在指定位置插入新行或段落。

**参数：**
- `filename` (str): Word文档路径
- `target_text` (str, 可选): 目标文本
- `line_text` (str, 可选): 要插入的文本
- `position` (str): 插入位置，'before' 或 'after'（默认）
- `line_style` (str, 可选): 段落样式
- `target_paragraph_index` (int, 可选): 目标段落索引

**返回：** 插入操作结果消息

### insert_numbered_list_near_text
在指定位置插入编号列表。

**参数：**
- `filename` (str): Word文档路径
- `target_text` (str, 可选): 目标文本
- `list_items` (list, 可选): 列表项文本数组
- `position` (str): 插入位置，'before' 或 'after'（默认）
- `target_paragraph_index` (int, 可选): 目标段落索引

**返回：** 插入操作结果消息

### get_paragraph_text_from_document_tool
获取指定段落的文本内容。

**参数：**
- `filename` (str): Word文档路径
- `paragraph_index` (int): 段落索引（从0开始）

**返回：** JSON格式的段落信息

### find_text_in_document_tool
在文档中查找指定文本的所有出现位置。

**参数：**
- `filename` (str): Word文档路径
- `text_to_find` (str): 要搜索的文本
- `match_case` (bool): 是否区分大小写（默认True）
- `whole_word` (bool): 是否只匹配完整单词（默认False）

**返回：** JSON格式的搜索结果，包含位置信息和出现次数

## 注意事项

### 文件权限
- 服务会自动检查文件是否可写
- 如果文件被其他程序锁定，会返回相应错误信息
- 建议在编辑前关闭Word文档

### 文档格式
- 自动处理 `.docx` 扩展名
- 支持现有文档的修改
- 保持原有文档格式和样式

### TOC处理
- 智能跳过目录(TOC)段落，避免意外修改
- 在文本搜索和替换时自动排除TOC内容

## 错误处理

常见错误及解决方案：

- **文档不存在**: 确保文件路径正确
- **文件不可写**: 检查文件权限或关闭正在使用该文件的程序
- **无效段落索引**: 段落索引从0开始，确保不超出文档范围
- **样式不存在**: 使用标准Word样式名称

## 开发信息

- **版本**: 1.0.0
- **许可证**: MIT
- **Python版本**: 3.10+
- **主要依赖**: python-docx, fastmcp, lxml, Pillow

## 相关项目

本项目是Word文档处理MCP服务系列的一部分：
- `word文档管理` - 文档创建、复制、信息获取
- `Word文档基础编辑` - 基础内容添加和格式化
- `word内容编辑` - 高级内容编辑和定位插入（本项目）
