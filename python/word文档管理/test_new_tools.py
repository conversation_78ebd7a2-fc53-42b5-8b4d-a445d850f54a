#!/usr/bin/env python3
"""
测试新添加的Word文档管理工具
"""
import asyncio
import os
import sys
from docx import Document

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from word_document_management.tools import (
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document,
    create_document
)


async def test_new_tools():
    """测试新添加的工具函数"""
    print("开始测试新添加的Word文档管理工具...")
    
    # 创建测试文档
    test_filename = "test_document.docx"
    print(f"\n1. 创建测试文档: {test_filename}")
    result = await create_document(test_filename, "测试文档", "测试作者")
    print(f"创建结果: {result}")
    
    # 添加一些内容到测试文档
    if os.path.exists(test_filename):
        doc = Document(test_filename)
        doc.add_heading('第一章 介绍', 1)
        doc.add_paragraph('这是第一段内容，包含一些测试文本。')
        doc.add_paragraph('这是第二段内容，也包含测试文本。')
        doc.add_heading('第二章 详细内容', 1)
        doc.add_paragraph('这里有更多的内容用于搜索测试。')
        doc.save(test_filename)
        print("已向测试文档添加内容")
    
    # 测试 get_document_outline
    print(f"\n2. 测试 get_document_outline")
    outline_result = await get_document_outline(test_filename)
    print(f"文档结构: {outline_result}")
    
    # 测试 get_paragraph_text_from_document
    print(f"\n3. 测试 get_paragraph_text_from_document")
    paragraph_result = await get_paragraph_text_from_document(test_filename, 0)
    print(f"第0段内容: {paragraph_result}")
    
    paragraph_result = await get_paragraph_text_from_document(test_filename, 1)
    print(f"第1段内容: {paragraph_result}")
    
    # 测试 find_text_in_document
    print(f"\n4. 测试 find_text_in_document")
    search_result = await find_text_in_document(test_filename, "测试", True, False)
    print(f"搜索'测试'的结果: {search_result}")
    
    search_result = await find_text_in_document(test_filename, "内容", False, True)
    print(f"搜索'内容'(完整单词)的结果: {search_result}")
    
    # 清理测试文件
    if os.path.exists(test_filename):
        os.remove(test_filename)
        print(f"\n已删除测试文件: {test_filename}")
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(test_new_tools())
