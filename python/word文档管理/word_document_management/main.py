"""
Word文档管理 MCP服务主程序

提供Word文档的基础管理功能，包括创建、复制、信息获取等操作。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    create_document,
    copy_document,
    get_document_info,
    get_document_text,
    list_available_documents,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)





# 初始化FastMCP服务器
mcp = FastMCP("Word文档管理")


def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    async def create_document_tool(filename: str, title: str = None, author: str = None):
        """创建新的Word文档，可选择设置元数据"""
        return await create_document(filename, title, author)

    @mcp.tool()
    async def copy_document_tool(source_filename: str, destination_filename: str = None):
        """创建Word文档的副本"""
        return await copy_document(source_filename, destination_filename)

    @mcp.tool()
    async def get_document_info_tool(filename: str):
        """获取Word文档的信息"""
        return await get_document_info(filename)

    @mcp.tool()
    async def get_document_text_tool(filename: str):
        """从Word文档中提取所有文本"""
        return await get_document_text(filename)

    @mcp.tool()
    async def list_available_documents_tool(directory: str = "."):
        """列出指定目录中的所有.docx文件"""
        return await list_available_documents(directory)

    @mcp.tool()
    async def get_document_outline_tool(filename: str):
        """获取Word文档的结构信息"""
        return await get_document_outline(filename)

    @mcp.tool()
    async def get_paragraph_text_from_document_tool(filename: str, paragraph_index: int):
        """获取Word文档中特定段落的文本"""
        return await get_paragraph_text_from_document(filename, paragraph_index)

    @mcp.tool()
    async def find_text_in_document_tool(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False):
        """在Word文档中查找指定文本的出现位置"""
        return await find_text_in_document(filename, text_to_find, match_case, whole_word)


def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word文档管理MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word文档管理服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
