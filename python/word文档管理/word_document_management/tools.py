"""
Word文档管理工具 - 提供文档创建、复制、信息获取等基础管理功能
"""
import os
import json
from typing import Optional
from docx import Document

from .utils import (
    check_file_writeable,
    ensure_docx_extension,
    create_document_copy,
    get_document_properties,
    extract_document_text,
    get_document_structure,
    get_paragraph_text,
    find_text
)


async def create_document(filename: str, title: Optional[str] = None, author: Optional[str] = None) -> str:
    """
    创建新的Word文档，可选择设置标题和作者信息
    
    Args:
        filename: 要创建的文档名称（可带或不带.docx扩展名）
        title: 文档标题（可选）
        author: 文档作者（可选）
    """
    filename = ensure_docx_extension(filename)
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法创建文档: {error_message}"
    
    try:
        # 创建新文档
        doc = Document()
        
        # 设置文档属性
        if title:
            doc.core_properties.title = title
        if author:
            doc.core_properties.author = author
        
        # 保存文档
        doc.save(filename)
        
        return f"成功创建文档: {filename}"
    except Exception as e:
        return f"创建文档失败: {str(e)}"


async def copy_document(source_filename: str, destination_filename: Optional[str] = None) -> str:
    """
    创建Word文档的副本
    
    Args:
        source_filename: 源文档文件名
        destination_filename: 目标文档文件名（可选）
    """
    source_filename = ensure_docx_extension(source_filename)
    
    if destination_filename:
        destination_filename = ensure_docx_extension(destination_filename)
    
    success, message, new_filepath = create_document_copy(source_filename, destination_filename)
    
    if success:
        return f"文档复制成功: {message}"
    else:
        return f"文档复制失败: {message}"


async def get_document_info(filename: str) -> str:
    """
    获取Word文档的信息
    
    Args:
        filename: Word文档路径
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    try:
        properties = get_document_properties(filename)
        return json.dumps(properties, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"获取文档信息失败: {str(e)}"


async def get_document_text(filename: str) -> str:
    """
    从Word文档中提取所有文本
    
    Args:
        filename: Word文档路径
    """
    filename = ensure_docx_extension(filename)
    
    return extract_document_text(filename)


async def list_available_documents(directory: str = ".") -> str:
    """
    列出指定目录中的所有.docx文件
    
    Args:
        directory: 要搜索Word文档的目录
    """
    try:
        if not os.path.exists(directory):
            return f"目录 {directory} 不存在"
        
        docx_files = [f for f in os.listdir(directory) if f.endswith('.docx')]
        
        if not docx_files:
            return f"在 {directory} 中未找到Word文档"
        
        result = f"在 {directory} 中找到 {len(docx_files)} 个Word文档:\n"
        for file in docx_files:
            file_path = os.path.join(directory, file)
            size = os.path.getsize(file_path) / 1024  # KB
            result += f"- {file} ({size:.2f} KB)\n"
        
        return result
    except Exception as e:
        return f"列出文档失败: {str(e)}"


async def get_document_outline(filename: str) -> str:
    """
    获取Word文档的结构信息

    Args:
        filename: Word文档路径
    """
    filename = ensure_docx_extension(filename)

    structure = get_document_structure(filename)
    return json.dumps(structure, indent=2, ensure_ascii=False)


async def get_paragraph_text_from_document(filename: str, paragraph_index: int) -> str:
    """
    获取Word文档中特定段落的文本

    Args:
        filename: Word文档路径
        paragraph_index: 段落索引（从0开始）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if paragraph_index < 0:
        return "无效参数: paragraph_index必须是非负整数"

    try:
        result = get_paragraph_text(filename, paragraph_index)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"获取段落文本失败: {str(e)}"


async def find_text_in_document(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> str:
    """
    在Word文档中查找指定文本的出现位置

    Args:
        filename: Word文档路径
        text_to_find: 要在文档中搜索的文本
        match_case: 是否区分大小写（True）或忽略大小写（False）
        whole_word: 是否只匹配完整单词（True）或子字符串（False）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if not text_to_find:
        return "搜索文本不能为空"

    try:
        result = find_text(filename, text_to_find, match_case, whole_word)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"搜索文本失败: {str(e)}"
