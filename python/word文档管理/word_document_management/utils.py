"""
文档管理工具类 - 提供文件操作和文档处理的基础功能
"""
import os
import json
import shutil
from typing import Dict, List, Any, Tuple, Optional
from docx import Document
import zipfile


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def create_document_copy(source_path: str, dest_path: Optional[str] = None) -> Tuple[bool, str, Optional[str]]:
    """
    创建文档副本
    
    Args:
        source_path: 源文档路径
        dest_path: 目标文档路径，如果未提供，将使用 source_path + '_copy.docx'
        
    Returns:
        Tuple of (success, message, new_filepath)
    """
    if not os.path.exists(source_path):
        return False, f"源文档 {source_path} 不存在", None
    
    if not dest_path:
        # 如果未提供目标路径，生成新文件名
        base, ext = os.path.splitext(source_path)
        dest_path = f"{base}_copy{ext}"
    
    try:
        # 简单文件复制
        shutil.copy2(source_path, dest_path)
        return True, f"文档已复制到 {dest_path}", dest_path
    except Exception as e:
        return False, f"复制文档失败: {str(e)}", None


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename


def get_document_properties(doc_path: str) -> Dict[str, Any]:
    """获取Word文档的属性信息"""
    if not os.path.exists(doc_path):
        return {"error": f"文档 {doc_path} 不存在"}
    
    try:
        doc = Document(doc_path)
        core_props = doc.core_properties
        
        return {
            "title": core_props.title or "",
            "author": core_props.author or "",
            "subject": core_props.subject or "",
            "keywords": core_props.keywords or "",
            "created": str(core_props.created) if core_props.created else "",
            "modified": str(core_props.modified) if core_props.modified else "",
            "last_modified_by": core_props.last_modified_by or "",
            "revision": core_props.revision or 0,
            "page_count": len(doc.sections),
            "word_count": sum(len(paragraph.text.split()) for paragraph in doc.paragraphs),
            "paragraph_count": len(doc.paragraphs),
            "table_count": len(doc.tables)
        }
    except Exception as e:
        return {"error": f"获取文档属性失败: {str(e)}"}


def extract_document_text(doc_path: str) -> str:
    """从Word文档中提取所有文本"""
    if not os.path.exists(doc_path):
        return f"文档 {doc_path} 不存在"
    
    try:
        doc = Document(doc_path)
        text = []
        
        for paragraph in doc.paragraphs:
            text.append(paragraph.text)
            
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        text.append(paragraph.text)
        
        return "\n".join(text)
    except Exception as e:
        return f"提取文本失败: {str(e)}"


def get_document_structure(doc_path: str) -> Dict[str, Any]:
    """获取Word文档的结构信息"""
    if not os.path.exists(doc_path):
        return {"error": f"文档 {doc_path} 不存在"}
    
    try:
        doc = Document(doc_path)
        structure = {
            "paragraphs": [],
            "tables": []
        }
        
        # 获取段落信息
        for i, para in enumerate(doc.paragraphs):
            structure["paragraphs"].append({
                "index": i,
                "text": para.text[:100] + ("..." if len(para.text) > 100 else ""),
                "style": para.style.name if para.style else "Normal"
            })
        
        # 获取表格信息
        for i, table in enumerate(doc.tables):
            structure["tables"].append({
                "index": i,
                "rows": len(table.rows),
                "columns": len(table.columns),
                "style": table.style.name if table.style else "Table Grid"
            })
        
        return structure
    except Exception as e:
        return {"error": f"获取文档结构失败: {str(e)}"}


def get_document_xml(doc_path: str) -> str:
    """提取并返回Word文档的原始XML结构 (word/document.xml)"""
    if not os.path.exists(doc_path):
        return f"文档 {doc_path} 不存在"
    try:
        with zipfile.ZipFile(doc_path) as docx_zip:
            with docx_zip.open('word/document.xml') as xml_file:
                return xml_file.read().decode('utf-8')
    except Exception as e:
        return f"提取XML失败: {str(e)}"


def get_paragraph_text(doc_path: str, paragraph_index: int) -> Dict[str, Any]:
    """
    获取Word文档中特定段落的文本

    Args:
        doc_path: Word文档路径
        paragraph_index: 段落索引（从0开始）

    Returns:
        包含段落文本和元数据的字典
    """
    if not os.path.exists(doc_path):
        return {"error": f"文档 {doc_path} 不存在"}

    try:
        doc = Document(doc_path)

        # 检查段落索引是否有效
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return {"error": f"无效的段落索引: {paragraph_index}。文档有 {len(doc.paragraphs)} 个段落。"}

        paragraph = doc.paragraphs[paragraph_index]

        return {
            "index": paragraph_index,
            "text": paragraph.text,
            "style": paragraph.style.name if paragraph.style else "Normal",
            "is_heading": paragraph.style.name.startswith("Heading") if paragraph.style else False
        }
    except Exception as e:
        return {"error": f"获取段落文本失败: {str(e)}"}


def find_text(doc_path: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> Dict[str, Any]:
    """
    在Word文档中查找所有指定文本的出现位置

    Args:
        doc_path: Word文档路径
        text_to_find: 要搜索的文本
        match_case: 是否区分大小写
        whole_word: 是否只匹配完整单词

    Returns:
        包含搜索结果的字典
    """
    if not os.path.exists(doc_path):
        return {"error": f"文档 {doc_path} 不存在"}

    if not text_to_find:
        return {"error": "搜索文本不能为空"}

    try:
        doc = Document(doc_path)
        results = {
            "query": text_to_find,
            "match_case": match_case,
            "whole_word": whole_word,
            "occurrences": [],
            "total_count": 0
        }

        # 在段落中搜索
        for i, para in enumerate(doc.paragraphs):
            # 准备用于比较的文本
            para_text = para.text
            search_text = text_to_find

            if not match_case:
                para_text = para_text.lower()
                search_text = search_text.lower()

            # 查找所有出现位置
            start_pos = 0
            while True:
                if whole_word:
                    # 对于完整单词搜索，需要检查单词边界
                    words = para_text.split()
                    found = False
                    for word_idx, word in enumerate(words):
                        if (word == search_text or
                            (not match_case and word.lower() == search_text.lower())):
                            results["occurrences"].append({
                                "paragraph_index": i,
                                "position": word_idx,
                                "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                            })
                            results["total_count"] += 1
                            found = True

                    # 检查完所有单词后跳出
                    break
                else:
                    # 对于子字符串搜索
                    pos = para_text.find(search_text, start_pos)
                    if pos == -1:
                        break

                    results["occurrences"].append({
                        "paragraph_index": i,
                        "position": pos,
                        "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                    })
                    results["total_count"] += 1
                    start_pos = pos + len(search_text)

        # 在表格中搜索
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    for para_idx, para in enumerate(cell.paragraphs):
                        # 准备用于比较的文本
                        para_text = para.text
                        search_text = text_to_find

                        if not match_case:
                            para_text = para_text.lower()
                            search_text = search_text.lower()

                        # 查找所有出现位置
                        start_pos = 0
                        while True:
                            if whole_word:
                                # 对于完整单词搜索，检查单词边界
                                words = para_text.split()
                                found = False
                                for word_idx, word in enumerate(words):
                                    if (word == search_text or
                                        (not match_case and word.lower() == search_text.lower())):
                                        results["occurrences"].append({
                                            "location": f"表格 {table_idx}, 行 {row_idx}, 列 {col_idx}",
                                            "position": word_idx,
                                            "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                                        })
                                        results["total_count"] += 1
                                        found = True

                                # 检查完所有单词后跳出
                                break
                            else:
                                # 对于子字符串搜索
                                pos = para_text.find(search_text, start_pos)
                                if pos == -1:
                                    break

                                results["occurrences"].append({
                                    "location": f"表格 {table_idx}, 行 {row_idx}, 列 {col_idx}",
                                    "position": pos,
                                    "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                                })
                                results["total_count"] += 1
                                start_pos = pos + len(search_text)

        return results
    except Exception as e:
        return {"error": f"搜索文本失败: {str(e)}"}
