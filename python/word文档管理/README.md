# Word文档管理 MCP服务

专门用于Word文档基础管理操作的MCP服务，提供文档的创建、复制、信息获取等核心功能。

## 功能特性

### 📄 文档管理工具 (5个)
- `create_document_tool` - 创建新的Word文档，支持设置标题和作者元数据
- `copy_document_tool` - 创建Word文档的副本，支持自定义目标文件名
- `get_document_info_tool` - 获取文档基本信息和元数据（作者、创建时间、页数等）
- `get_document_text_tool` - 从Word文档中提取所有文本内容（包括表格文本）
- `list_available_documents_tool` - 列出指定目录中的所有Word文档及其大小信息

## 技术特性

- 基于 **FastMCP** 框架构建，提供高性能的MCP服务
- 使用 **python-docx** 库进行Word文档操作
- 支持文件权限检查和错误处理
- 自动处理文档扩展名（.docx）规范化
- 智能文档复制和命名策略

## 安装和配置

### 环境要求
- Python 3.10+
- 依赖包：python-docx, fastmcp, lxml

### 本地安装
```bash
cd python/word文档管理
pip install -e .
```

### 使用uv安装（推荐）
```bash
cd python/word文档管理
uv sync
```

### Claude Desktop配置
在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "word-document-management": {
      "command": "python",
      "args": ["-m", "word_document_management.main"],
      "cwd": "/path/to/python/word文档管理"
    }
  }
}
```

## 使用示例

### 基础文档操作
- "创建一个名为'项目报告'的新文档，标题设为'2024年度项目总结'"
- "复制'模板.docx'文档为'新报告.docx'"
- "获取'报告.docx'文档的基本信息"
- "提取'合同.docx'中的所有文本内容"
- "列出当前目录下的所有Word文档"

### 实际使用场景
```
用户: "创建一个新的Word文档叫做'会议纪要'，作者设为'张三'"
助手: 使用create_document_tool创建文档

用户: "把'模板.docx'复制一份叫做'新项目计划.docx'"
助手: 使用copy_document_tool复制文档

用户: "告诉我'报告.docx'这个文档的详细信息"
助手: 使用get_document_info_tool获取文档元数据
```

## API参考

### create_document_tool
**功能**: 创建新的Word文档，可选择设置标题和作者信息
**参数**:
- `filename` (str): 文档文件名（自动添加.docx扩展名）
- `title` (str, 可选): 文档标题
- `author` (str, 可选): 文档作者

### copy_document_tool
**功能**: 创建Word文档的副本
**参数**:
- `source_filename` (str): 源文档文件名
- `destination_filename` (str, 可选): 目标文档文件名，未提供时自动生成

### get_document_info_tool
**功能**: 获取文档的详细信息和元数据
**参数**:
- `filename` (str): Word文档路径
**返回**: JSON格式的文档信息，包括作者、创建时间、修改时间、页数、字数、段落数、表格数等

### get_document_text_tool
**功能**: 从Word文档中提取所有文本内容
**参数**:
- `filename` (str): Word文档路径
**返回**: 文档中的所有文本内容，包括表格中的文本

### list_available_documents_tool
**功能**: 列出指定目录中的所有Word文档
**参数**:
- `directory` (str, 默认"."): 要搜索的目录路径
**返回**: 目录中所有.docx文件的列表及其大小信息

## 错误处理

服务包含完善的错误处理机制：
- 文件不存在检查
- 文件权限验证
- 自动扩展名处理
- 详细的错误信息返回

## 注意事项

1. 所有文件名会自动添加.docx扩展名（如果未提供）
2. 复制文档时，如果未指定目标文件名，会自动生成"原文件名_copy.docx"
3. 服务仅支持.docx格式的Word文档
4. 确保有足够的文件系统权限进行读写操作
