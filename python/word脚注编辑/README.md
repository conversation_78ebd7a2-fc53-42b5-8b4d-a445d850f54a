# Word脚注编辑 MCP服务

专门用于Word文档脚注管理的MCP服务，提供脚注的添加、删除等基础编辑功能。

## 功能特性

### 📝 脚注编辑工具 (3个)
- `add_footnote_after_text_tool` - 在指定文本后添加脚注标记
- `add_footnote_to_document_tool` - 向特定段落索引位置添加脚注
- `delete_footnote_from_document_tool` - 删除文档中包含特定文本的脚注

## 技术特性

- 基于 **FastMCP** 框架构建，提供高性能的MCP服务
- 使用 **python-docx** 库进行Word文档操作
- 支持文件权限检查和错误处理
- 自动处理文档扩展名（.docx）规范化
- 简化的脚注实现，使用文本标记格式 `[脚注: 内容]`

## 安装和配置

### 环境要求
- Python 3.10+
- 依赖包：python-docx, fastmcp, lxml

### 本地安装
```bash
cd python/word脚注编辑
pip install -e .
```

### 使用uv安装（推荐）
```bash
cd python/word脚注编辑
uv sync
```

### Claude Desktop配置
在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "word-footnote-management": {
      "command": "python",
      "args": ["-m", "word_footnote_management.main"],
      "cwd": "/path/to/python/word脚注编辑"
    }
  }
}
```

## 使用示例

### 基础脚注操作
- "在'重要概念'这个文本后添加脚注'这是一个关键定义'"
- "在第3个段落添加脚注'参考资料：学术论文'"
- "删除包含'参考资料'的脚注"

### 实际使用场景
```
用户: "在'人工智能'这个词后面加个脚注，内容是'AI技术的发展历程'"
助手: 使用add_footnote_after_text_tool在指定文本后添加脚注

用户: "在第5个段落的末尾添加脚注'数据来源：2024年统计报告'"
助手: 使用add_footnote_to_document_tool向特定段落添加脚注

用户: "删除所有包含'临时注释'的脚注"
助手: 使用delete_footnote_from_document_tool删除指定脚注
```

## API参考

### add_footnote_after_text_tool
**功能**: 在指定文本后添加脚注标记
**参数**:
- `filename` (str): Word文档文件名（自动添加.docx扩展名）
- `target_text` (str): 要在其后添加脚注的目标文本
- `footnote_text` (str): 脚注内容
**返回**: 操作结果消息

### add_footnote_to_document_tool
**功能**: 向特定段落索引位置添加脚注
**参数**:
- `filename` (str): Word文档文件名
- `paragraph_index` (int): 段落索引（从0开始）
- `footnote_text` (str): 脚注内容
**返回**: 操作结果消息，包含段落总数验证

### delete_footnote_from_document_tool
**功能**: 删除文档中包含特定文本的脚注
**参数**:
- `filename` (str): Word文档文件名
- `footnote_text` (str): 要删除的脚注文本内容
**返回**: 删除操作结果，包含删除的脚注数量

## 脚注格式说明

本服务使用简化的脚注格式：`[脚注: 内容]`

**示例**：
- 原文：`人工智能技术发展迅速`
- 添加脚注后：`人工智能技术发展迅速 [脚注: AI技术包括机器学习、深度学习等]`

## 错误处理

服务包含完善的错误处理机制：
- 文件存在性检查
- 文件权限验证
- 段落索引范围验证
- 目标文本查找验证
- 详细的错误信息返回

## 注意事项

1. **文件格式**: 仅支持.docx格式的Word文档
2. **脚注格式**: 使用文本标记格式，不是Word的原生脚注功能
3. **段落索引**: 从0开始计数，需要确保索引在有效范围内
4. **文本匹配**: 目标文本匹配是精确匹配，区分大小写
5. **文件权限**: 确保有足够的文件系统权限进行读写操作
6. **备份建议**: 建议在编辑重要文档前先创建备份
