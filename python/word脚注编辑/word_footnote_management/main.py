"""
word脚注编辑 MCP服务主程序

Word脚注编辑MCP服务 - 提供脚注和尾注相关功能
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    add_footnote_after_text,
    add_footnote_to_document,
    delete_footnote_from_document
)

# 初始化FastMCP服务器
mcp = FastMCP("word脚注编辑")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def add_footnote_after_text_tool(filename: str, target_text: str, footnote_text: str):
        """在指定文本后添加脚注"""
        return add_footnote_after_text(filename, target_text, footnote_text)

    @mcp.tool()
    def add_footnote_to_document_tool(filename: str, paragraph_index: int, footnote_text: str):
        """向特定段落添加脚注"""
        return add_footnote_to_document(filename, paragraph_index, footnote_text)

    @mcp.tool()
    def delete_footnote_from_document_tool(filename: str, footnote_text: str):
        """删除文档中的脚注"""
        return delete_footnote_from_document(filename, footnote_text)

def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word脚注编辑MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word脚注编辑服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
