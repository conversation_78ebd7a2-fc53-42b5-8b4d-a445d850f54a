"""
Word脚注管理工具 - 提供脚注和尾注相关功能（精简版）
"""
import os
from typing import Optional
from docx import Document

from .utils import check_file_writeable, ensure_docx_extension


def add_footnote_after_text(filename: str, target_text: str, footnote_text: str) -> str:
    """在指定文本后添加脚注"""
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}"
    
    try:
        doc = Document(filename)
        found = False
        
        for paragraph in doc.paragraphs:
            if target_text in paragraph.text:
                paragraph.add_run(f" [脚注: {footnote_text}]")
                found = True
                break
        
        if not found:
            return f"未找到目标文本: {target_text}"
        
        doc.save(filename)
        return f"脚注已添加到文本 '{target_text}' 后"
    except Exception as e:
        return f"添加脚注失败: {str(e)}"


def add_footnote_to_document(filename: str, paragraph_index: int, footnote_text: str) -> str:
    """向特定段落添加脚注"""
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}"
    
    try:
        doc = Document(filename)
        
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return f"无效的段落索引。文档有 {len(doc.paragraphs)} 个段落"
        
        paragraph = doc.paragraphs[paragraph_index]
        paragraph.add_run(f" [脚注: {footnote_text}]")
        
        doc.save(filename)
        return f"脚注已添加到段落 {paragraph_index}"
    except Exception as e:
        return f"添加脚注失败: {str(e)}"


def delete_footnote_from_document(filename: str, footnote_text: str) -> str:
    """删除文档中的脚注"""
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}"
    
    try:
        doc = Document(filename)
        removed_count = 0
        
        for paragraph in doc.paragraphs:
            original_text = paragraph.text
            new_text = original_text.replace(f" [脚注: {footnote_text}]", "")
            if new_text != original_text:
                paragraph.clear()
                paragraph.add_run(new_text)
                removed_count += 1
        
        doc.save(filename)
        return f"已删除 {removed_count} 个脚注"
    except Exception as e:
        return f"删除脚注失败: {str(e)}"
