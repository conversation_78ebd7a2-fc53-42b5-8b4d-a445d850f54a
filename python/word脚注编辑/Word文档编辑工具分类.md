# Word文档编辑工具分类

本文档整理了Word文档编辑相关的所有工具，按功能分类便于查阅和使用。

## 📁 文档管理工具 (5个)

### 基础文档操作
- **`create_document_tool`** - 创建新的Word文档
  - 支持设置标题和作者元数据
  - 可自定义文档基本信息

- **`copy_document_tool`** - 创建Word文档的副本
  - 支持自定义目标文件名
  - 保持原文档格式和内容

### 文档信息获取
- **`get_document_info_tool`** - 获取文档基本信息和元数据
  - 作者信息
  - 创建时间
  - 页数统计等

- **`get_document_text_tool`** - 从Word文档中提取所有文本内容
  - 包括表格文本
  - 纯文本格式输出

- **`list_available_documents_tool`** - 列出指定目录中的所有Word文档
  - 显示文件大小信息
  - 便于文档管理

---

## ✏️ 内容编辑工具 (7个)

### 文本操作
- **`search_and_replace_tool`** - 在文档中搜索并替换文本
  - 支持批量替换
  - 精确匹配功能

- **`find_text_in_document_tool`** - 在文档中查找文本并返回位置信息
  - 定位文本位置
  - 返回详细位置信息

### 内容插入
- **`insert_header_near_text`** - 在指定文本或段落索引附近插入标题
  - 支持多级标题
  - 灵活定位插入位置

- **`insert_line_or_paragraph_near_text`** - 在指定位置插入新行或段落
  - 精确位置控制
  - 支持多种插入模式

- **`insert_numbered_list_near_text`** - 在指定位置插入编号列表
  - 自动编号功能
  - 支持多级列表

### 表格操作
- **`add_table_tool`** - 向文档添加表格
  - 支持自定义行列数
  - 支持数据填充

### 段落管理
- **`get_paragraph_text_from_document_tool`** - 获取指定段落的文本内容
  - 精确段落定位
  - 文本内容提取

---

## 🎨 样式文本工具 (2个)

- **`create_custom_style_tool`** - 创建自定义样式
  - 自定义字体、颜色、大小
  - 可重复使用的样式模板

- **`format_text_tool`** - 格式化段落中的特定文本范围
  - 精确文本范围选择
  - 多种格式化选项

---

## 📊 表格样式处理工具

### 🔧 表格基础格式化工具 (1个)
- **`format_table_tool`** - 使用边框、阴影和结构格式化表格
  - 支持设置标题行
  - 边框样式自定义
  - 单元格阴影效果
  - 提供多种边框样式选择

### 🎨 表格美化工具 (3个)
- **`set_table_cell_shading_tool`** - 为特定表格单元格应用阴影/填充
  - 支持十六进制颜色和颜色名称
  - 支持多种阴影模式
  - 精确单元格定位

- **`apply_table_alternating_rows_tool`** - 为表格应用交替行颜色
  - 提高表格可读性
  - 可自定义奇偶行颜色
  - 自动应用到整个表格

- **`highlight_table_header_tool`** - 为表格标题行应用特殊高亮
  - 可自定义背景色和文字颜色
  - 突出表格标题
  - 增强视觉效果

### 🔧 表格对齐与布局工具 (3个)
- **`set_table_cell_alignment_tool`** - 设置特定表格单元格的文本对齐
  - **水平对齐**：左对齐、居中、右对齐、两端对齐
  - **垂直对齐**：顶部、居中、底部
  - 精确单元格控制

- **`set_entire_table_alignment_tool`** - 设置整个表格所有单元格的文本对齐方式
  - 批量设置所有单元格的对齐方式
  - 统一表格样式
  - 提高效率

- **`set_table_column_width_tool`** - 设置特定表格列的宽度
  - 支持多种单位：磅、英寸、厘米、百分比、自动
  - 精确列宽控制
  - 优化表格布局

---

## 📝 脚注编辑工具 (3个)

- **`add_footnote_after_text_tool`** - 在指定文本后添加脚注标记
  - 精确文本定位
  - 自动脚注编号
  - 标准脚注格式

- **`add_footnote_to_document_tool`** - 向特定段落索引位置添加脚注
  - 段落索引定位
  - 灵活插入位置
  - 支持自定义脚注内容

- **`delete_footnote_from_document_tool`** - 删除文档中包含特定文本的脚注
  - 智能文本匹配
  - 安全删除功能
  - 自动重新编号

---

## 📈 工具统计总览

| 分类 | 工具数量 | 主要功能 |
|------|----------|----------|
| 文档管理 | 5个 | 文档创建、复制、信息获取 |
| 内容编辑 | 7个 | 文本编辑、内容插入、表格添加 |
| 样式文本 | 2个 | 自定义样式、文本格式化 |
| 表格样式 | 7个 | 表格美化、对齐、布局控制 |
| 脚注编辑 | 3个 | 脚注添加、删除、管理 |
| **总计** | **24个** | **完整的Word文档编辑解决方案** |

---

## 🚀 使用建议

1. **文档创建流程**：使用文档管理工具创建和管理文档
2. **内容编辑流程**：使用内容编辑工具添加和修改文档内容
3. **样式美化流程**：使用样式工具和表格工具美化文档外观
4. **脚注管理流程**：使用脚注工具添加学术引用和补充说明

通过这些工具的组合使用，可以实现专业级的Word文档编辑和格式化功能。
